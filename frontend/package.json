{"name": "finsync360-frontend", "version": "1.0.0", "description": "FinSync360 Frontend React Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-datepicker": "^4.25.0", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-dropzone": "^14.2.3", "qrcode.react": "^3.1.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "moment": "^2.29.4", "numeral": "^2.0.6", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/numeral": "^2.0.5", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}